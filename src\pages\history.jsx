import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

export default function History() {
  const [bookings, setBookings] = useState([]);
  const [user, setUser] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is logged in
    const userData = localStorage.getItem('user');
    if (!userData) {
      navigate('/login');
      return;
    }

    setUser(JSON.parse(userData));

    // Get bookings from localStorage
    const allBookings = JSON.parse(localStorage.getItem('bookings') || '[]');
    setBookings(allBookings);
  }, [navigate]);



  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-ubud-dark-green mb-4">Please login to view your booking history</h2>
          <button
            onClick={() => navigate('/login')}
            className="bg-ubud-dark-green text-white px-6 py-3 rounded-lg hover:bg-ubud-light-green transition-colors"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-green-50 to-yellow-50">
      <div className="container mx-auto px-6 py-8">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-4xl font-bold text-center bg-gradient-to-r from-ubud-dark-green via-blue-600 to-purple-600 bg-clip-text text-transparent mb-8">Order History</h1>

          {bookings.length === 0 ? (
            <div className="bg-gradient-to-br from-white to-blue-50 rounded-2xl shadow-xl p-8 text-center border border-blue-100">
              <div className="text-6xl mb-4">📋</div>
              <h2 className="text-2xl font-bold text-gray-700 mb-4">No Booking History</h2>
              <p className="text-gray-500 mb-6">You haven't made any bookings yet.</p>
              <button
                onClick={() => navigate('/services')}
                className="bg-gradient-to-r from-ubud-dark-green to-blue-600 text-white px-8 py-3 rounded-xl hover:from-ubud-light-green hover:to-blue-500 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                Browse Services
              </button>
            </div>
          ) : (
            <div className="space-y-8">
              {bookings.map((booking, index) => (
                <div key={booking.id} className={`bg-gradient-to-br ${index % 3 === 0 ? 'from-blue-50 to-indigo-100' : index % 3 === 1 ? 'from-green-50 to-emerald-100' : 'from-purple-50 to-pink-100'} rounded-2xl shadow-xl overflow-hidden border-2 ${index % 3 === 0 ? 'border-blue-200' : index % 3 === 1 ? 'border-green-200' : 'border-purple-200'} hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1`}>
                  <div className={`bg-gradient-to-r ${index % 3 === 0 ? 'from-blue-500 to-indigo-600' : index % 3 === 1 ? 'from-green-500 to-emerald-600' : 'from-purple-500 to-pink-600'} p-4`}>
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="text-2xl font-bold text-white mb-1">
                          Booking #{booking.id}
                        </h3>
                        <p className="text-white/90 text-sm">
                          Booked on: {new Date(booking.bookingDate).toLocaleDateString('id-ID')}
                        </p>
                      </div>
                      <span className={`px-4 py-2 rounded-full text-sm font-bold bg-white/20 text-white backdrop-blur-sm border border-white/30`}>
                        {booking.status?.charAt(0).toUpperCase() + booking.status?.slice(1) || 'Confirmed'}
                      </span>
                    </div>
                  </div>
                  <div className="p-6">

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                      <div className="bg-white/60 backdrop-blur-sm rounded-xl p-5 border border-white/50">
                        <h4 className="font-bold text-gray-800 mb-4 text-lg flex items-center">
                          <span className="mr-2">👤</span>
                          Booking Details
                        </h4>
                        <div className="space-y-3 text-sm">
                          <div className="flex items-center">
                            <span className="font-semibold text-gray-700 w-16">Name:</span>
                            <span className="text-gray-800 font-medium">{booking.name}</span>
                          </div>
                          <div className="flex items-center">
                            <span className="font-semibold text-gray-700 w-16">Phone:</span>
                            <span className="text-gray-800 font-medium">{booking.phone}</span>
                          </div>
                          <div className="flex items-center">
                            <span className="font-semibold text-gray-700 w-16">Date:</span>
                            <span className="text-gray-800 font-medium">{booking.date}</span>
                          </div>
                          <div className="flex items-center">
                            <span className="font-semibold text-gray-700 w-16">Guests:</span>
                            <span className="text-gray-800 font-medium">1 person</span>
                          </div>
                        </div>
                      </div>

                      <div className="bg-white/60 backdrop-blur-sm rounded-xl p-5 border border-white/50">
                        <h4 className="font-bold text-gray-800 mb-4 text-lg flex items-center">
                          <span className="mr-2">🎯</span>
                          Activities
                        </h4>
                        <div className="space-y-3">
                          {booking.selectedActivities?.map((activity, actIndex) => (
                            <div key={actIndex} className={`bg-gradient-to-r ${actIndex % 2 === 0 ? 'from-blue-100 to-blue-200' : 'from-green-100 to-green-200'} rounded-lg p-3 border ${actIndex % 2 === 0 ? 'border-blue-300' : 'border-green-300'}`}>
                              <div className="flex justify-between items-center">
                                <div>
                                  <span className="font-semibold text-gray-800">{activity.name}</span>
                                  <div className="text-xs text-gray-600 mt-1">
                                    1 person × Rp. {activity.price?.toLocaleString('id-ID')}
                                  </div>
                                </div>
                                <span className="font-bold text-gray-800 bg-white/70 px-3 py-1 rounded-full">
                                  Rp. {activity.price?.toLocaleString('id-ID')}
                                </span>
                              </div>
                            </div>
                          ))}
                          <div className="border-t-2 border-gray-300 pt-3 mt-4">
                            <div className="flex justify-between items-center bg-gradient-to-r from-yellow-100 to-orange-100 rounded-lg p-3 border-2 border-yellow-300">
                              <span className="font-bold text-gray-800 text-lg">Total</span>
                              <span className="font-bold text-2xl text-orange-600">
                                Rp. {booking.total?.toLocaleString('id-ID')}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {booking.paymentDate && (
                      <div className="mt-6">
                        <div className="bg-gradient-to-r from-green-100 to-emerald-100 rounded-xl p-4 border-2 border-green-200">
                          <p className="text-sm text-gray-700 flex items-center">
                            <span className="mr-2">💳</span>
                            <span className="font-semibold">Payment Date:</span>
                            <span className="ml-2 font-medium text-green-700">
                              {new Date(booking.paymentDate).toLocaleDateString('id-ID')}
                            </span>
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
