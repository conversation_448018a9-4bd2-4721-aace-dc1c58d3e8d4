import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

export default function History() {
  const [bookings, setBookings] = useState([]);
  const [user, setUser] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is logged in
    const userData = localStorage.getItem('user');
    if (!userData) {
      navigate('/login');
      return;
    }

    setUser(JSON.parse(userData));

    // Get bookings from localStorage
    const allBookings = JSON.parse(localStorage.getItem('bookings') || '[]');
    setBookings(allBookings);
  }, [navigate]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-ubud-dark-green mb-4">Please login to view your booking history</h2>
          <button
            onClick={() => navigate('/login')}
            className="bg-ubud-dark-green text-white px-6 py-3 rounded-lg hover:bg-ubud-light-green transition-colors"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-ubud-light-green/20 to-ubud-dark-green/20">
      <div className="container mx-auto px-6 py-8">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-4xl font-bold text-ubud-dark-green mb-8 text-center bg-gradient-to-r from-ubud-dark-green to-ubud-light-green bg-clip-text text-transparent">
            Order History
          </h1>

          {bookings.length === 0 ? (
            <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-8 text-center border border-ubud-light-green/20">
              <div className="text-6xl mb-4">📋</div>
              <h2 className="text-2xl font-bold text-ubud-dark-green mb-4">No Booking History</h2>
              <p className="text-gray-600 mb-6">You haven't made any bookings yet.</p>
              <button
                onClick={() => navigate('/services')}
                className="bg-gradient-to-r from-ubud-dark-green to-ubud-light-green text-white px-8 py-3 rounded-xl hover:shadow-lg transition-all duration-300 font-semibold"
              >
                Browse Services
              </button>
            </div>
          ) : (
            <div className="space-y-6">
              {bookings.map((booking) => (
                <div key={booking.id} className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl overflow-hidden border border-ubud-light-green/20 hover:shadow-3xl transition-all duration-300">
                  <div className="bg-gradient-to-r from-ubud-dark-green to-ubud-light-green p-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="text-xl font-bold text-white mb-1">
                          Booking #{booking.id}
                        </h3>
                        <p className="text-ubud-cream text-sm">
                          Booked on: {new Date(booking.bookingDate).toLocaleDateString('id-ID')}
                        </p>
                      </div>
                      <span className={`px-4 py-2 rounded-full text-sm font-semibold shadow-lg ${getStatusColor(booking.status)} border-2 border-white/20`}>
                        {booking.status?.charAt(0).toUpperCase() + booking.status?.slice(1) || 'Confirmed'}
                      </span>
                    </div>
                  </div>

                  <div className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="bg-gradient-to-br from-ubud-light-green/10 to-ubud-dark-green/10 rounded-xl p-4 border border-ubud-light-green/20">
                        <h4 className="font-bold text-ubud-dark-green mb-3 flex items-center">
                          <span className="mr-2">👤</span>
                          Booking Details
                        </h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between items-center bg-white/50 rounded-lg p-2">
                            <span className="text-gray-700 font-medium">Name:</span>
                            <span className="font-semibold text-ubud-dark-green">{booking.name}</span>
                          </div>
                          <div className="flex justify-between items-center bg-white/50 rounded-lg p-2">
                            <span className="text-gray-700 font-medium">Phone:</span>
                            <span className="font-semibold text-ubud-dark-green">{booking.phone}</span>
                          </div>
                          <div className="flex justify-between items-center bg-white/50 rounded-lg p-2">
                            <span className="text-gray-700 font-medium">Date:</span>
                            <span className="font-semibold text-ubud-dark-green">{booking.date}</span>
                          </div>
                        </div>
                      </div>

                      <div className="bg-gradient-to-br from-ubud-yellow/10 to-ubud-light-green/10 rounded-xl p-4 border border-ubud-yellow/20">
                        <h4 className="font-bold text-ubud-dark-green mb-3 flex items-center">
                          <span className="mr-2">🎯</span>
                          Activities
                        </h4>
                        <div className="space-y-2">
                          {booking.selectedActivities?.map((activity, index) => (
                            <div key={index} className="bg-white/70 rounded-lg p-3 border border-ubud-light-green/20">
                              <div className="flex justify-between items-center">
                                <div>
                                  <span className="font-medium text-ubud-dark-green">{activity.name}</span>
                                  <div className="text-xs text-gray-600 mt-1">
                                    1 person × Rp. {activity.price?.toLocaleString('id-ID')}
                                  </div>
                                </div>
                                <span className="font-bold text-ubud-dark-green bg-ubud-yellow/20 px-2 py-1 rounded-lg">
                                  Rp. {activity.price?.toLocaleString('id-ID')}
                                </span>
                              </div>
                            </div>
                          ))}
                          <div className="border-t-2 border-ubud-dark-green/20 pt-3 mt-3">
                            <div className="flex justify-between items-center font-bold bg-gradient-to-r from-ubud-dark-green to-ubud-light-green text-white rounded-lg p-3">
                              <span>Total</span>
                              <span className="text-ubud-yellow">
                                Rp. {booking.total?.toLocaleString('id-ID')}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {booking.paymentDate && (
                      <div className="mt-6 pt-4 border-t-2 border-ubud-light-green/20">
                        <div className="bg-gradient-to-r from-green-50 to-ubud-light-green/10 rounded-lg p-3 border border-green-200">
                          <p className="text-sm text-ubud-dark-green font-medium flex items-center">
                            <span className="mr-2">💳</span>
                            <span className="font-semibold">Payment Date:</span>
                            <span className="ml-2 font-bold">
                              {new Date(booking.paymentDate).toLocaleDateString('id-ID')}
                            </span>
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
